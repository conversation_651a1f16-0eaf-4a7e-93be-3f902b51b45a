package com.inspeedia.vanning.domain;

import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Objects;

/**
 * PlannedWork entity representing planned work in the VMA system
 *
 * This entity stores information about planned work including date, platform
 * details, timing, and size information.
 */
@Table("planned_work")
public class PlannedWork extends BaseEntity {

    @NotNull(message = "Date is required")
    @Column("date")
    private LocalDate date;

    @NotBlank(message = "Van GP is required")
    @Size(min = 2, max = 2, message = "Van GP must be exactly 2 characters")
    @Pattern(regexp = "^[A-Z]{2}$", message = "Van GP must be 2 uppercase alphabets")
    @Column("van_gp")
    private String vanGp;

    @NotBlank(message = "Delivery platform is required")
    @Size(min = 1, max = 1, message = "Delivery platform must be exactly 1 character")
    @Pattern(regexp = "^[A-Z]$", message = "Delivery platform must be 1 uppercase alphabet")
    @Column("delivery_platform")
    private String deliveryPlatform;

    @NotBlank(message = "Collection platform is required")
    @Size(min = 1, max = 1, message = "Collection platform must be exactly 1 character")
    @Pattern(regexp = "^[A-Z]$", message = "Collection platform must be 1 uppercase alphabet")
    @Column("collection_platform")
    private String collectionPlatform;

    @NotNull(message = "Load time is required")
    @Column("load_time")
    private LocalTime loadTime;

    @NotBlank(message = "Size is required")
    @Size(min = 2, max = 2, message = "Size must be exactly 2 characters")
    @Pattern(regexp = "^[A-Z0-9]{2}$", message = "Size must be 2 alphanumeric characters")
    @Column("size")
    private String size;

    @NotNull(message = "Start time is required")
    @Column("start_time")
    private LocalTime startTime;

    @NotNull(message = "End time is required")
    @Column("end_time")
    private LocalTime endTime;

    @NotNull(message = "Duration is required")
    @Column("duration")
    private LocalTime duration;

    // Default constructor
    public PlannedWork() {
        super();
    }

    // Constructor with all fields
    public PlannedWork(LocalDate date, String vanGp, String deliveryPlatform, String collectionPlatform,
            LocalTime loadTime, String size, LocalTime startTime, LocalTime endTime, LocalTime duration) {
        super();
        this.date = date;
        this.vanGp = vanGp;
        this.deliveryPlatform = deliveryPlatform;
        this.collectionPlatform = collectionPlatform;
        this.loadTime = loadTime;
        this.size = size;
        this.startTime = startTime;
        this.endTime = endTime;
        this.duration = duration;
    }

    // Constructor with all fields including base entity fields
    public PlannedWork(Long id, LocalDateTime createdAt, LocalDateTime updatedAt, Long version,
            String createdBy, String updatedBy, boolean deleted,
            LocalDate date, String vanGp, String deliveryPlatform, String collectionPlatform,
            LocalTime loadTime, String size, LocalTime startTime, LocalTime endTime, LocalTime duration) {
        super(id, createdAt, updatedAt, version, createdBy, updatedBy, deleted);
        this.date = date;
        this.vanGp = vanGp;
        this.deliveryPlatform = deliveryPlatform;
        this.collectionPlatform = collectionPlatform;
        this.loadTime = loadTime;
        this.size = size;
        this.startTime = startTime;
        this.endTime = endTime;
        this.duration = duration;
    }

    // Getters
    public LocalDate getDate() {
        return date;
    }

    public String getVanGp() {
        return vanGp;
    }

    public String getDeliveryPlatform() {
        return deliveryPlatform;
    }

    public String getCollectionPlatform() {
        return collectionPlatform;
    }

    public LocalTime getLoadTime() {
        return loadTime;
    }

    public String getSize() {
        return size;
    }

    public LocalTime getStartTime() {
        return startTime;
    }

    public LocalTime getEndTime() {
        return endTime;
    }

    public LocalTime getDuration() {
        return duration;
    }

    // Setters
    public void setDate(LocalDate date) {
        this.date = date;
    }

    public void setVanGp(String vanGp) {
        this.vanGp = vanGp;
    }

    public void setDeliveryPlatform(String deliveryPlatform) {
        this.deliveryPlatform = deliveryPlatform;
    }

    public void setCollectionPlatform(String collectionPlatform) {
        this.collectionPlatform = collectionPlatform;
    }

    public void setLoadTime(LocalTime loadTime) {
        this.loadTime = loadTime;
    }

    public void setSize(String size) {
        this.size = size;
    }

    public void setStartTime(LocalTime startTime) {
        this.startTime = startTime;
    }

    public void setEndTime(LocalTime endTime) {
        this.endTime = endTime;
    }

    public void setDuration(LocalTime duration) {
        this.duration = duration;
    }

    // equals method (calls super)
    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        PlannedWork that = (PlannedWork) o;
        return Objects.equals(date, that.date)
                && Objects.equals(vanGp, that.vanGp)
                && Objects.equals(deliveryPlatform, that.deliveryPlatform)
                && Objects.equals(collectionPlatform, that.collectionPlatform)
                && Objects.equals(loadTime, that.loadTime)
                && Objects.equals(size, that.size)
                && Objects.equals(startTime, that.startTime)
                && Objects.equals(endTime, that.endTime)
                && Objects.equals(duration, that.duration);
    }

    // hashCode method (calls super)
    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), date, vanGp, deliveryPlatform, collectionPlatform,
                loadTime, size, startTime, endTime, duration);
    }

    // toString method (calls super)
    @Override
    public String toString() {
        return "PlannedWork{"
                + "date=" + date
                + ", vanGp='" + vanGp + '\''
                + ", deliveryPlatform='" + deliveryPlatform + '\''
                + ", collectionPlatform='" + collectionPlatform + '\''
                + ", loadTime=" + loadTime
                + ", size='" + size + '\''
                + ", startTime=" + startTime
                + ", endTime=" + endTime
                + ", duration=" + duration
                + ", " + super.toString()
                + '}';
    }
}
