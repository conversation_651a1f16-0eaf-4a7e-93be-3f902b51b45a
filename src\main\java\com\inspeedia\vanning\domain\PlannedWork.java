package com.inspeedia.vanning.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import java.time.LocalDate;
import java.time.LocalTime;

/**
 * PlannedWork entity representing planned work in the VMA system
 *
 * This entity stores information about planned work including date, platform
 * details, timing, and size information.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Table("planned_work")
public class PlannedWork extends BaseEntity {

    @NotNull(message = "Date is required")
    @Column("date")
    private LocalDate date;

    @NotBlank(message = "Van GP is required")
    @Size(min = 2, max = 2, message = "Van GP must be exactly 2 characters")
    @Pattern(regexp = "^[A-Z]{2}$", message = "Van GP must be 2 uppercase alphabets")
    @Column("van_gp")
    private String vanGp;

    @NotBlank(message = "Delivery platform is required")
    @Size(min = 1, max = 1, message = "Delivery platform must be exactly 1 character")
    @Pattern(regexp = "^[A-Z]$", message = "Delivery platform must be 1 uppercase alphabet")
    @Column("delivery_platform")
    private String deliveryPlatform;

    @NotBlank(message = "Collection platform is required")
    @Size(min = 1, max = 1, message = "Collection platform must be exactly 1 character")
    @Pattern(regexp = "^[A-Z]$", message = "Collection platform must be 1 uppercase alphabet")
    @Column("collection_platform")
    private String collectionPlatform;

    @NotNull(message = "Load time is required")
    @Column("load_time")
    private LocalTime loadTime;

    @NotBlank(message = "Size is required")
    @Size(min = 2, max = 2, message = "Size must be exactly 2 characters")
    @Pattern(regexp = "^[A-Z0-9]{2}$", message = "Size must be 2 alphanumeric characters")
    @Column("size")
    private String size;

    @NotNull(message = "Start time is required")
    @Column("start_time")
    private LocalTime startTime;

    @NotNull(message = "End time is required")
    @Column("end_time")
    private LocalTime endTime;

    @NotNull(message = "Duration is required")
    @Column("duration")
    private LocalTime duration;
}
