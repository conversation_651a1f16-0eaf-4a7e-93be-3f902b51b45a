plugins {
    id 'org.springframework.boot' version '3.5.5'
    id 'io.spring.dependency-management' version '1.1.7'
    id 'java'
}

group = 'com.inspeedia.vanning'
version = '0.0.1-SNAPSHOT'

java {
  toolchain {
    languageVersion = JavaLanguageVersion.of(17)
  }
}

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
}

repositories {
    mavenCentral()
}

dependencies {
    // Spring Boot Starters
    implementation 'org.springframework.boot:spring-boot-starter-webflux'
    implementation 'org.springframework.boot:spring-boot-starter-data-r2dbc'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'org.springframework.boot:spring-boot-starter-security'
    
    // R2DBC MSSQL Driver
    implementation 'io.r2dbc:r2dbc-mssql:1.0.2.RELEASE'
    
    // Jackson for JSON processing
    implementation 'com.fasterxml.jackson.core:jackson-databind'
    implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310'
    
    // Logging
    implementation 'net.logstash.logback:logstash-logback-encoder:7.4'
    
    // Micrometer for metrics
    implementation 'io.micrometer:micrometer-registry-prometheus'
    
    // OpenAPI/Swagger documentation
    implementation 'org.springdoc:springdoc-openapi-starter-webflux-ui:2.2.0'
    
    // Lombok removed - using manual implementations instead
    
    // Development tools
    developmentOnly 'org.springframework.boot:spring-boot-devtools'
    
    // Configuration processor
    annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'
    
    // Test dependencies
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'io.projectreactor:reactor-test'
    testImplementation 'io.r2dbc:r2dbc-h2'
    testImplementation 'org.springframework.security:spring-security-test'
    testImplementation 'org.testcontainers:junit-jupiter'
    testImplementation 'org.testcontainers:r2dbc'
    testImplementation 'org.testcontainers:mssqlserver'
}

tasks.named('test') {
    useJUnitPlatform()
}

// JVM arguments for better performance
tasks.withType(JavaCompile) {
    options.compilerArgs += [
        '-parameters'
    ]
}

// Gradle wrapper
wrapper {
    gradleVersion = '8.5'
}
