# VMA API - Vanning Management Application

A reactive Spring Boot API for managing actual work and planned work using WebFlux, R2DBC, and MSSQL Server.

## Features

- **Reactive Programming**: Built with Spring WebFlux for non-blocking, reactive operations
- **Database**: R2DBC with MSSQL Server for reactive database operations
- **Security**: Basic authentication with configurable users
- **Documentation**: OpenAPI 3.0 with Swagger UI
- **Monitoring**: Actuator endpoints with Prometheus metrics
- **Logging**: Structured JSON logging with Logback
- **Exception Handling**: Global exception handler with standardized error responses
- **Validation**: Bean validation with detailed error messages

## Technology Stack

- **Java 17**
- **Spring Boot 3.2.0**
- **Spring WebFlux** (Reactive Web)
- **Spring Data R2DBC** (Reactive Database Access)
- **MSSQL Server** (Database)
- **Gradle** (Build Tool)
- **Lombok** (Code Generation)
- **OpenAPI 3.0** (API Documentation)

## Prerequisites

- Java 17 or higher
- MSSQL Server 2019 or higher
- Gradle 8.5 or higher (or use the wrapper)

## Quick Start

### 1. Database Setup

Create a database named `vma_db` in your MSSQL Server instance.

### 2. Configuration

Update the database connection in `src/main/resources/application.yml`:

```yaml
spring:
  r2dbc:
    url: r2dbc:mssql://localhost:1433/vma_db
    username: your_username
    password: your_password
```

### 3. Build and Run

```bash
# Build the application
./gradlew build

# Run the application
./gradlew bootRun
```

The application will start on port 8080.

## API Documentation

Once the application is running, you can access:

- **Swagger UI**: http://localhost:8080/swagger-ui.html
- **OpenAPI JSON**: http://localhost:8080/api-docs
- **Actuator Health**: http://localhost:8080/actuator/health

## Authentication

The API uses basic authentication with the following default credentials:

- **Admin**: username=`admin`, password=`admin123`
- **User**: username=`user`, password=`user123`

## API Endpoints

### Actual Work Management

- `GET /api/v1/actual-work` - Get all active actual work records
- `POST /api/v1/actual-work` - Create a new actual work record
- `GET /api/v1/actual-work/{id}` - Get actual work by ID
- `PUT /api/v1/actual-work/{id}` - Update actual work
- `DELETE /api/v1/actual-work/{id}` - Delete actual work
- `GET /api/v1/actual-work/user/{userName}` - Get actual work by user name
- `GET /api/v1/actual-work/search?q={term}` - Search actual work by user name
- `GET /api/v1/actual-work/progress/{progress}` - Get actual work by progress
- `GET /api/v1/actual-work/high-progress` - Get high progress work (>= 8)
- `GET /api/v1/actual-work/low-progress` - Get low progress work (<= 3)

### Planned Work Management

- `GET /api/v1/planned-work` - Get all active planned work records
- `POST /api/v1/planned-work` - Create a new planned work record
- `GET /api/v1/planned-work/{id}` - Get planned work by ID
- `PUT /api/v1/planned-work/{id}` - Update planned work
- `DELETE /api/v1/planned-work/{id}` - Delete planned work
- `GET /api/v1/planned-work/date/{date}` - Get planned work by date
- `GET /api/v1/planned-work/van-gp/{vanGp}` - Get planned work by van GP
- `GET /api/v1/planned-work/today` - Get today's planned work
- `GET /api/v1/planned-work/upcoming` - Get upcoming planned work

## Configuration Profiles

- **Development**: `spring.profiles.active=dev`
- **Production**: `spring.profiles.active=prod`

## Monitoring

The application includes comprehensive monitoring:

- **Actuator endpoints** for health checks and metrics
- **Prometheus metrics** at `/actuator/prometheus`
- **Structured JSON logging** with trace IDs
- **Global exception handling** with standardized error responses

## Testing

```bash
# Run all tests
./gradlew test

# Run tests with coverage
./gradlew test jacocoTestReport
```

## Building for Production

```bash
# Build JAR file
./gradlew bootJar

# The JAR will be created in build/libs/
java -jar build/libs/vma-api-0.0.1-SNAPSHOT.jar
```

## Architecture

The application follows industry-standard patterns:

- **Layered Architecture**: Controller → Service → Repository
- **Reactive Programming**: Non-blocking I/O with Reactor
- **Domain-Driven Design**: Clear domain models and boundaries
- **SOLID Principles**: Clean, maintainable code structure

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
