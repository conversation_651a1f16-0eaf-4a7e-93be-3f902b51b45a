package com.inspeedia.vanning.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.time.LocalTime;

/**
 * ActualWork entity representing actual work performed in the VMA system
 *
 * This entity stores information about actual work including user, time
 * details, and progress information.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Table("actual_work")
public class ActualWork extends BaseEntity {

    @NotBlank(message = "User name is required")
    @Size(max = 100, message = "User name must not exceed 100 characters")
    @Column("user_name")
    private String userName;

    @NotNull(message = "Start time is required")
    @Column("start_time")
    private LocalTime startTime;

    @NotNull(message = "End time is required")
    @Column("end_time")
    private LocalTime endTime;

    @NotNull(message = "Duration is required")
    @Column("duration")
    private LocalTime duration;

    @NotNull(message = "Progress is required")
    @Min(value = 0, message = "Progress must be between 0 and 10")
    @Max(value = 10, message = "Progress must be between 0 and 10")
    @Column("progress")
    private Integer progress;

    @NotNull(message = "Progress rate is required")
    @DecimalMin(value = "0.0", message = "Progress rate must be between 0.0 and 100.0")
    @DecimalMax(value = "100.0", message = "Progress rate must be between 0.0 and 100.0")
    @Column("progress_rate")
    private Float progressRate;
}
