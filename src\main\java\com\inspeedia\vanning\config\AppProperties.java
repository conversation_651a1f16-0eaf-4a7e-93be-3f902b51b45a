package com.inspeedia.vanning.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Application configuration properties
 *
 * This class maps the application-specific configuration properties from
 * application.yml to Java objects.
 */
@Data
@Component
@ConfigurationProperties(prefix = "app")
public class AppProperties {

    private Cors cors = new Cors();
    private Api api = new Api();
    private Pagination pagination = new Pagination();

    @Data
    public static class Cors {

        private List<String> allowedOrigins;
        private List<String> allowedMethods;
        private List<String> allowedHeaders;
        private boolean allowCredentials;
        private long maxAge;
    }

    @Data
    public static class Api {

        private String version;
        private String basePath;
    }

    @Data
    public static class Pagination {

        private int defaultPageSize = 20;
        private int maxPageSize = 100;
    }
}
