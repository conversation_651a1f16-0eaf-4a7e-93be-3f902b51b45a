# Lombok configuration for VMA API project
# This file configures Lombok behavior and ensures compatibility

# Enable annotation processing
lombok.addLombokGeneratedAnnotation = true

# Configure logging
lombok.log.fieldName = log
lombok.log.fieldIsStatic = true

# Configure equals and hashcode
lombok.equalsAndHashCode.callSuper = call

# Configure toString
lombok.toString.callSuper = call

# Disable some features that might cause issues
lombok.accessors.chain = false
lombok.accessors.fluent = false

# Configure copy annotations
lombok.copyableAnnotations += org.springframework.data.annotation.Id
lombok.copyableAnnotations += org.springframework.data.relational.core.mapping.Column
lombok.copyableAnnotations += org.springframework.data.relational.core.mapping.Table
lombok.copyableAnnotations += jakarta.validation.constraints.NotNull
lombok.copyableAnnotations += jakarta.validation.constraints.NotBlank
lombok.copyableAnnotations += jakarta.validation.constraints.Size
lombok.copyableAnnotations += jakarta.validation.constraints.Min
lombok.copyableAnnotations += jakarta.validation.constraints.Max
lombok.copyableAnnotations += jakarta.validation.constraints.DecimalMin
lombok.copyableAnnotations += jakarta.validation.constraints.DecimalMax
lombok.copyableAnnotations += jakarta.validation.constraints.Pattern

# Ensure compatibility with newer Java versions
lombok.extern.findbugs.addSuppressFBWarnings = true
