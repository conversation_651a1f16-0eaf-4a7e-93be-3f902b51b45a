package com.inspeedia.vanning.domain;

import lombok.Data;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.annotation.Version;

import java.time.LocalDateTime;

/**
 * Base entity class with common audit fields
 *
 * This abstract class provides common fields like ID, timestamps, and version
 * for optimistic locking that are shared across all entities.
 */
@Data
public abstract class BaseEntity {

    @Id
    private Long id;

    @CreatedDate
    private LocalDateTime createdAt;

    @LastModifiedDate
    private LocalDateTime updatedAt;

    @Version
    private Long version;

    private String createdBy;
    private String updatedBy;
    private boolean deleted = false;
}
